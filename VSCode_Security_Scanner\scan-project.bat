@echo off
REM 🔒 Universal VS Code Security Scanner - Windows Launcher
REM Usage: scan-project.bat [project-path] [scan-type]

setlocal

REM Set scanner location
set SCANNER_DIR=C:\Users\<USER>\Time_Stamp_Project\VSCode_Security_Scanner
set SCANNER_SCRIPT=%SCANNER_DIR%\scanner.js

echo 🔒 Universal VS Code Security Scanner
echo =====================================

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Get project path (use current directory if not provided)
if "%~1"=="" (
    set PROJECT_PATH=%cd%
) else (
    set PROJECT_PATH=%~1
)

REM Get scan type (default to quick)
if "%~2"=="" (
    set SCAN_TYPE=--quick
) else (
    set SCAN_TYPE=--%~2
)

echo 📁 Scanning: %PROJECT_PATH%
echo 🔍 Scan Type: %SCAN_TYPE%
echo.

REM Run the scanner
node "%SCANNER_SCRIPT%" %SCAN_TYPE% --path "%PROJECT_PATH%"

if %errorlevel% equ 0 (
    echo.
    echo ✅ Scan completed successfully!
) else (
    echo.
    echo ❌ Scan failed with errors
)

echo.
echo 💡 Available scan types:
echo    quick        - Fast vulnerability and license scan
echo    full         - Complete security analysis with reports
echo    report       - Generate detailed HTML/JSON reports
echo    sbom         - Generate Software Bill of Materials
echo    licenses     - Check license compliance only
echo    vulnerabilities - Check vulnerabilities only
echo.
echo 📊 View dashboard: %SCANNER_DIR%\dashboard.html
echo.

pause
